# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚀 Quick Commands

```bash
cd web                                    # Navigate to the main app directory
bun run dev                               # Start development server (Vite)
bun run build                             # Build for production
bun run preview                           # Preview production build
bun run typecheck                         # Run TypeScript type checking
bun run deploy                            # Deploy to Cloudflare Workers
bun run lint                              # Check code quality with Biome
bun run lint:fix                          # Fix auto-fixable issues with Biome
bun run format                            # Format code with Biome
```

## 🛠️ Tech Stack

- **Frontend Framework**: React 19 with TypeScript
- **Build Tool**: Vite 6.0
- **Routing**: React Router v7
- **Styling**: Tailwind CSS with custom design tokens
- **UI Components**: Radix UI primitives with custom styling
- **AI Integration**: Vercel AI SDK with OpenAI
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Development**: Strict TypeScript, Biome for linting/formatting
- **Deployment**: Cloudflare Workers
- **AI Providers**: OpenAI and OpenRouter integration

## 📁 Project Architecture

The project follows a clean React SPA structure in the `web/` directory:

```
web/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── ui/              # Radix-based design system components
│   │   ├── Layout.tsx       # Root layout wrapper
│   │   └── *-mockup.tsx     # Phone mockup components
│   ├── pages/               # Route-level page components
│   │   ├── LandingPage.tsx  # Homepage with hero, features
│   │   ├── ImageAnalyzer.tsx # Photo analysis tool
│   │   └── BioAnalyzer.tsx  # Bio optimization tool
│   ├── hooks/               # Custom React hooks
│   ├── lib/                 # Utility functions
│   ├── routes.tsx           # React Router configuration
│   ├── main.tsx             # App entry point
│   └── globals.css          # Global styles & CSS variables
├── public/                  # Static assets
└── configuration files
```

## 🎨 Design System

### Comprehensive Design System
A unified design system built on Tailwind CSS and shadcn/ui with:

#### Color Palette
- **Brand Colors**: Flame Red (`#FF5851`), Sparks Pink (`#FF8A80`)
- **Neutral Colors**: Graphite shades, Cloud White
- **Semantic Colors**: Success Green, Warning Amber, Error Crimson, Info Blue
- **System Colors**: CSS custom properties for light/dark themes

#### Typography System
- **Font Families**: Inter (primary), JetBrains Mono (code)
- **Typography Scale**: Display (3.5rem), H1-H6, Body (lg/md/sm), Caption
- **Responsive**: Mobile-optimized font sizes with consistent line heights
- **Utility Classes**: Typography helpers for consistent text styling

#### Component Standards
- **Buttons**: 7 variants (primary, secondary, tertiary, ghost, outline, destructive, link)
- **Forms**: Enhanced inputs with consistent styling and focus states
- **Cards**: Standardized container components with header/content/footer
- **Layout**: Container utilities and responsive grid systems

#### Design Tokens
- **Spacing Scale**: 8-step system (xs: 4px to 4xl: 96px)
- **Border Radius**: Consistent radius scale (sm: 4px to xl: 24px)
- **Shadows**: Elevation system with light/dark theme variants
- **CSS Variables**: 50+ design tokens for colors, spacing, typography

#### Implementation
- **CSS Custom Properties**: Comprehensive token system
- **Tailwind Integration**: All tokens available as utility classes
- **Theme Support**: Complete light/dark theme implementation
- **Component Showcase**: Interactive design system documentation

## 🧩 Key Patterns

### Routing Structure
- Uses React Router v7 with nested routes
- Layout component wraps all pages via `<Outlet />`
- Route configuration centralized in `src/routes.tsx`

### Component Composition
```typescript
// Standard component pattern
export function ComponentName() {
  return (
    <div className="custom-styles">
      {/* JSX content */}
    </div>
  );
}
```

### AI Integration
- Uses Vercel AI SDK (`ai` package) for AI features
- OpenAI integration via `@ai-sdk/openai`
- OpenRouter as additional AI provider via `@openrouter/ai-sdk-provider`
- Multi-step analysis agents with progress tracking
- Streaming responses for real-time analysis
- Service classes for business logic (`AnalysisService`)

### State Management
- Primarily uses React hooks (useState, useEffect)
- React Hook Form for complex form handling
- Local storage for image and analysis data persistence
- No global state management library currently used

## 🔧 Development Workflow

### File Organization
- Components are co-located by feature/page when possible
- Shared UI components in `components/ui/`
- Utilities in `lib/utils.ts`
- Global styles use CSS custom properties for theming

### TypeScript Configuration
- Strict mode enabled with additional checks
- Path aliases: `@/*` maps to `src/*`
- ES2020 target with modern module resolution

### Build & Deployment
- Vite handles bundling and HMR (using experimental `rolldown-vite`)
- PostCSS processes Tailwind CSS
- Cloudflare Workers deployment via Wrangler
- Production builds optimized for edge deployment

### Code Quality
- Biome for linting and formatting (replacing ESLint/Prettier)
- 2-space indentation, 100-character line width
- Strict TypeScript configuration with additional checks
- Import organization and type imports enforced

## 🎯 Application Context

TinderOP is a dating profile optimization tool that provides:
1. **Image Analysis**: AI-powered photo scoring and recommendations
2. **Bio Enhancement**: Bio writing assistance and optimization  
3. **Multi-step Analysis**: Progressive analysis with callback-based architecture

The app focuses on improving dating success through AI-driven recommendations and has a clean, modern interface that mirrors dating app aesthetics.

### Current Development Status
- **Active Branch**: `enhanced-bio-analyzer` (implementing multi-step AI analysis)
- **Recent Work**: Bio analyzer improvements following image analyzer patterns
- **Architecture**: Privacy-focused with local storage for sensitive data
- **Authentication**: Clerk integration prepared but currently commented out

## 📝 Git Commit Conventions

This project follows [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0-beta.2/) specification.

### Commit Message Format
```
<type>[optional scope]: <description>

[optional body]

[optional footer]
```

### Primary Commit Types
- `feat:` - Introduces a new feature (correlates with MINOR in semantic versioning)
- `fix:` - Patches a bug (correlates with PATCH in semantic versioning)
- `BREAKING CHANGE:` - Introduces an API-breaking change (correlates with MAJOR version)

### Additional Types
- `chore:` - Maintenance tasks, dependency updates
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, semicolons, etc.)
- `refactor:` - Code refactoring without changing functionality
- `perf:` - Performance improvements
- `test:` - Adding or updating tests
- `build:` - Changes to build system or dependencies
- `ci:` - Changes to CI configuration

### Examples
```bash
# Basic feature
feat: add dark mode toggle to settings

# Feature with scope
feat(bio-analyzer): implement multi-step AI analysis

# Bug fix
fix: resolve image upload validation error

# Breaking change
feat!: restructure API response format

BREAKING CHANGE: API now returns data in nested object structure

# Chore with scope
chore(deps): update React to v19

# Documentation
docs: add deployment instructions to README
```

### Commit Message Guidelines
- Use imperative mood ("add" not "added" or "adds")
- Keep description under 50 characters
- Use lowercase for type and description
- Include scope in parentheses when relevant
- Add body for complex changes
- Reference issues in footer when applicable