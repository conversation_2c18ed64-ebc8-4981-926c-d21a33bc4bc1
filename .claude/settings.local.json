{"permissions": {"allow": ["mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npm install:*)", "Bash(bun add:*)", "Bash(bun run:*)", "Bash(git pull:*)", "Bash(git rebase:*)", "Bash(git checkout:*)", "WebFetch(domain:www.conventionalcommits.org)", "Bash(git add:*)", "Bash(git push:*)", "Bash(gh pr create:*)", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git commit:*)", "Bash(gh pr view:*)", "<PERSON><PERSON>(gh pr edit:*)", "Bash(claude mcp add:*)"], "deny": []}}