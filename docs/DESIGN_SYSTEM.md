# TinderOP Design System

A comprehensive design system for the TinderOP web application, built on Tailwind CSS and shadcn/ui components.

## 🎨 Color Palette

### Primary Colors
Our brand colors inspired by <PERSON><PERSON>'s passionate energy:

- **Flame Red**: `#FF5851` - Primary brand color for CTAs and key interactions
- **Sparks Pink**: `#FF8A80` - Secondary brand color for accents and highlights
- **Gradient Primary**: `linear-gradient(135deg, #FF5851 0%, #FF8A80 100%)`

### Neutral Colors
Professional grays for text and backgrounds:

- **Graphite 90**: `#1A1A1A` - Primary text color
- **Graphite 60**: `#666666` - Secondary text color
- **Cloud White**: `#FFFFFF` - Primary background color

### Semantic Colors
Status and feedback colors:

- **Success Green**: `#28A745` - Success states and positive feedback
- **Warning Amber**: `#FFC107` - Warning states and caution
- **Error Crimson**: `#DC3545` - Error states and destructive actions
- **Info Blue**: `#17A2B8` - Informational content

### System Colors (CSS Variables)
Using HSL values for better theme support:

```css
/* Light Theme */
--background: 0 0% 100%;
--foreground: 0 0% 3.9%;
--primary: 0 0% 9%;
--secondary: 0 0% 96.1%;
--muted: 0 0% 96.1%;
--accent: 0 0% 96.1%;
--destructive: 0 84.2% 60.2%;
--border: 0 0% 89.8%;
--input: 0 0% 89.8%;
--ring: 0 0% 3.9%;

/* Dark Theme */
--background: 0 0% 3.9%;
--foreground: 0 0% 98%;
--primary: 0 0% 98%;
--secondary: 0 0% 14.9%;
--muted: 0 0% 14.9%;
--accent: 0 0% 14.9%;
--destructive: 0 62.8% 30.6%;
--border: 0 0% 14.9%;
--input: 0 0% 14.9%;
--ring: 0 0% 83.1%;
```

## 📝 Typography System

### Font Families
- **Primary**: Inter (system fallback: Arial, Helvetica, sans-serif)
- **Secondary**: JetBrains Mono (for code and technical content)

### Typography Scale

#### Display Text
- **Display 1**: 56px/64px, weight 700, letter-spacing -0.01em
- **Display 1 Mobile**: 40px/48px, weight 700, letter-spacing -0.01em

#### Headings
- **H1**: 40px/48px, weight 700, letter-spacing -0.01em
- **H1 Mobile**: 32px/40px, weight 700, letter-spacing -0.01em
- **H2**: 32px/40px, weight 600, letter-spacing -0.01em
- **H2 Mobile**: 24px/32px, weight 600, letter-spacing -0.01em
- **H3**: 24px/32px, weight 600
- **H4**: 20px/28px, weight 600
- **H5**: 18px/26px, weight 600
- **H6**: 16px/24px, weight 600

#### Body Text
- **Body Large**: 18px/28px, weight 400
- **Body Medium**: 16px/24px, weight 400
- **Body Small**: 14px/20px, weight 400
- **Caption**: 12px/16px, weight 400

### Text Color Usage
- **Primary Text**: `text-foreground` or `text-graphite-90`
- **Secondary Text**: `text-muted-foreground` or `text-graphite-60`
- **Brand Text**: `text-flame-red`
- **Inverse Text**: `text-cloud-white`

## 🧩 Component Design Standards

### Buttons

#### Primary Button
- Background: `bg-gradient-primary`
- Text: `text-cloud-white`
- Shadow: `shadow-button-primary`
- Hover: `-translate-y-px hover:shadow-lg`
- Height: 44px (h-11)
- Padding: 16px horizontal (px-4)

#### Secondary Button
- Border: `border-2 border-flame-red`
- Text: `text-flame-red`
- Background: `bg-cloud-white`
- Hover: `hover:bg-flame-red/10`

#### Tertiary Button
- Text: `text-flame-red`
- Hover: `hover:underline`
- No background or border

### Form Elements

#### Input Fields
- Border: `border border-input`
- Background: `bg-background`
- Text: `text-foreground`
- Placeholder: `placeholder:text-muted-foreground`
- Focus: `focus:ring-2 focus:ring-ring`
- Height: 40px (h-10)
- Padding: 12px horizontal (px-3)

#### Select Dropdowns
- Same styling as input fields
- Chevron icon: `text-muted-foreground`

#### Checkboxes & Radio Buttons
- Accent color: `accent-flame-red`
- Focus ring: `focus:ring-flame-red`

### Cards and Containers

#### Card Component
- Background: `bg-card`
- Border: `border border-border`
- Border radius: `rounded-lg` (16px)
- Shadow: `shadow-sm`
- Padding: `p-6` (24px)

#### Container Spacing
- Section padding: `py-16` (64px vertical)
- Container max-width: `max-w-7xl`
- Container padding: `px-6` (24px horizontal)

## 📐 Spacing and Layout

### Spacing Scale
Based on 4px increments:

- **xs**: 4px (`space-1`)
- **sm**: 8px (`space-2`)
- **md**: 16px (`space-4`)
- **lg**: 24px (`space-6`)
- **xl**: 32px (`space-8`)
- **2xl**: 48px (`space-12`)
- **3xl**: 64px (`space-16`)
- **4xl**: 96px (`space-24`)

### Grid System
- **Breakpoints**: 
  - sm: 640px
  - md: 768px
  - lg: 1024px
  - xl: 1280px
  - 2xl: 1536px
- **Container max-width**: 1120px (2xl breakpoint)
- **Grid columns**: 12-column system

### Border Radius
- **Small**: 4px (`rounded-sm`)
- **Medium**: 8px (`rounded-md`)
- **Large**: 16px (`rounded-lg`)
- **Extra Large**: 24px (`rounded-xl`)
- **Full**: 9999px (`rounded-full`)

### Shadows and Elevation
- **Small**: `shadow-sm` - Subtle elevation
- **Medium**: `shadow-md` - Standard elevation
- **Large**: `shadow-lg` - Prominent elevation
- **Button Primary**: `shadow-button-primary` - Custom button shadow

## 🌙 Theme Support

The design system supports both light and dark themes using CSS custom properties. Theme switching is handled by the `next-themes` provider.

### Theme Implementation
```tsx
import { ThemeProvider } from "@/components/theme-provider";

function App() {
  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      {/* Your app content */}
    </ThemeProvider>
  );
}
```

## 🔧 Implementation Guidelines

### CSS Custom Properties
All design tokens are implemented as CSS custom properties for easy maintenance and theme switching.

### Tailwind Configuration
Design tokens are integrated into the Tailwind configuration for consistent usage across components.

### Component Variants
Use `class-variance-authority` (cva) for creating component variants with consistent styling.

### Naming Conventions
- Use semantic naming for colors (primary, secondary, muted)
- Use descriptive names for custom colors (flame-red, sparks-pink)
- Follow BEM-like naming for component classes

## 📚 Usage Examples

### Button Usage
```tsx
<Button variant="primary" size="lg">
  Primary Action
</Button>

<Button variant="secondary">
  Secondary Action
</Button>

<Button variant="tertiary">
  Tertiary Action
</Button>
```

### Typography Usage
```tsx
<h1 className="text-display-1 md:text-display-1-mobile font-bold text-foreground">
  Main Heading
</h1>

<p className="text-body-lg text-muted-foreground">
  Body text content
</p>
```

### Layout Usage
```tsx
// Section containers
<section className="container-section">
  <div className="space-content">
    <h2 className="text-heading-2">Section Title</h2>
    <p className="text-body-large">Section description</p>

    <div className="responsive-grid-3">
      {/* Grid content */}
    </div>
  </div>
</section>

// Content containers
<div className="container-content">
  <div className="space-items">
    {/* Content items */}
  </div>
</div>
```

### Card Usage
```tsx
// Basic card
<Card>
  <CardHeader>
    <CardTitle>Feature Title</CardTitle>
    <CardDescription>Brief description of the feature</CardDescription>
  </CardHeader>
  <CardContent>
    <p className="text-body">Detailed content goes here</p>
  </CardContent>
</Card>

// Card with actions
<Card className="card-elevated">
  <CardHeader>
    <CardTitle>Analysis Result</CardTitle>
  </CardHeader>
  <CardContent>
    <p className="text-body">Your photo scored 8.5/10</p>
  </CardContent>
  <CardFooter>
    <Button variant="primary">View Details</Button>
    <Button variant="ghost">Share</Button>
  </CardFooter>
</Card>
```

### Form Usage
```tsx
<div className="space-items">
  <div className="space-tight">
    <Label htmlFor="email" className="input-label">
      Email Address
    </Label>
    <Input
      id="email"
      type="email"
      placeholder="Enter your email"
      className="input-field"
    />
    <p className="input-help">We'll never share your email</p>
  </div>

  <Button variant="primary" className="w-full">
    Sign Up
  </Button>
</div>
```

### Status Indicators
```tsx
// Success state
<div className="status-success">
  Analysis Complete
</div>

// Warning state
<div className="status-warning">
  Low Quality Image
</div>

// Error state
<div className="status-error">
  Upload Failed
</div>
```

## 🎯 Design System Showcase

To view all components and their variants in action, visit the design system showcase page:

```tsx
import { DesignSystemShowcase } from "@/pages/DesignSystemShowcase";

// Add to your routing configuration
```

## 🔧 Customization

### Adding New Colors
1. Add CSS custom properties to `globals.css`
2. Update Tailwind config with new color tokens
3. Document the new colors in this guide

### Creating New Components
1. Follow the established patterns using `cva` for variants
2. Use design system tokens for consistent styling
3. Include proper TypeScript types
4. Add to the showcase page

### Extending Typography
1. Add new font sizes to Tailwind config
2. Create utility classes in `design-system.css`
3. Update typography documentation

## 🚀 Getting Started

1. **Install Dependencies**: All design system dependencies are already included
2. **Import Styles**: The design system CSS is automatically imported
3. **Use Components**: Import components from `@/components/ui`
4. **Apply Utilities**: Use design system utility classes for consistent styling
5. **Follow Patterns**: Reference this guide and the showcase for implementation patterns
