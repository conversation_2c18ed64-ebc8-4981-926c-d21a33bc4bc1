// Health check endpoint for Tinder<PERSON> Helper
// Used by Chrome extension to verify API connectivity

import type { Env } from '../../types/env';

interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  service: string;
  version: string;
  timestamp: string;
  checks: {
    api: boolean;
    ai: boolean;
    environment: boolean;
  };
}

// CORS headers for cross-origin requests from extension
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Handle preflight requests
export async function onRequestOptions() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders,
  });
}

// Health check GET endpoint
export async function onRequestGet(context: { env: Env }) {
  try {
    console.log('🏥 Health check requested');
    
    // Perform health checks
    const checks = await performHealthChecks(context.env);
    
    // Determine overall status
    const allHealthy = Object.values(checks).every(check => check === true);
    const anyUnhealthy = Object.values(checks).some(check => check === false);
    
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (allHealthy) {
      status = 'healthy';
    } else if (anyUnhealthy) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }
    
    const response: HealthResponse = {
      status,
      service: 'TinderOP Helper API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      checks
    };
    
    const httpStatus = status === 'healthy' ? 200 : status === 'degraded' ? 206 : 503;
    
    console.log(`✅ Health check completed: ${status}`);

    return Response.json(response, {
      status: httpStatus,
      headers: corsHeaders,
    });
    
  } catch (error) {
    console.error('❌ Health check failed:', error);
    
    const errorResponse: HealthResponse = {
      status: 'unhealthy',
      service: 'TinderOP Helper API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      checks: {
        api: false,
        ai: false,
        environment: false
      }
    };
    
    return Response.json(errorResponse, {
      status: 503,
      headers: corsHeaders,
    });
  }
};

// Perform individual health checks
async function performHealthChecks(env: Env): Promise<{
  api: boolean;
  ai: boolean;
  environment: boolean;
}> {
  const checks = {
    api: true, // API is responsive if we're here
    ai: false,
    environment: false
  };
  
  try {
    // Check environment variables
    checks.environment = !!(env.OPENROUTER_API_KEY);
    
    // Check AI service connectivity (basic test)
    if (checks.environment) {
      try {
        // We could do a simple test call to OpenRouter here
        // For now, just verify the API key exists and looks valid
        const apiKey = env.OPENROUTER_API_KEY;
        checks.ai = !!(apiKey && apiKey.length > 10);
      } catch (error) {
        console.warn('⚠️ AI service check failed:', error);
        checks.ai = false;
      }
    }
    
  } catch (error) {
    console.error('❌ Health checks failed:', error);
  }
  
  return checks;
}