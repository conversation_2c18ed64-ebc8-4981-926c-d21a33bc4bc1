// TinderOP Helper - Conversation Analysis API Endpoint
// Cloudflare Workers API route for analyzing dating app conversations

import { openrouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";

// Types for the API
interface AnalyzeRequest {
  screenshot: string; // Base64 encoded image
  context: {
    platform: string;
    url: string;
    timestamp: number;
    messages: string[];
  };
  timestamp: number;
}

interface ConversationSuggestion {
  text: string;
  tone: 'witty' | 'sincere' | 'flirty' | 'casual' | 'thoughtful';
  confidence: number;
  reasoning?: string;
}

interface AnalyzeResponse {
  success: boolean;
  suggestions: ConversationSuggestion[];
  analysis?: {
    conversationTone: string;
    userPersonality: string;
    recommendedApproach: string;
  };
  processingTime: number;
  error?: string;
}

// CORS headers for cross-origin requests from extension
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

// Handle preflight requests
export const onRequestOptions: PagesFunction = async () => {
  return new Response(null, {
    status: 200,
    headers: corsHeaders,
  });
};

// Main POST handler for conversation analysis
export const onRequestPost: PagesFunction<Env> = async (context) => {
  const startTime = Date.now();
  
  try {
    console.log('🔍 TinderOP Helper: Received conversation analysis request');
    
    // Parse request body
    const body = await context.request.json() as AnalyzeRequest;
    
    // Validate request
    const validation = validateRequest(body);
    if (!validation.valid) {
      return createErrorResponse(validation.error || 'Invalid request', 400, startTime);
    }
    
    // Get API key from environment
    const apiKey = context.env.VITE_OPENROUTER_API_KEY;
    if (!apiKey) {
      console.error('❌ OpenRouter API key not found in environment');
      return createErrorResponse('API configuration error', 500, startTime);
    }
    
    // Analyze conversation with Gemini 2.5 Flash
    const suggestions = await analyzeConversationWithAI(body, apiKey);
    
    // Create response
    const response: AnalyzeResponse = {
      success: true,
      suggestions,
      analysis: await generateConversationAnalysis(body, apiKey),
      processingTime: Date.now() - startTime
    };
    
    console.log(`✅ Analysis completed in ${response.processingTime}ms, generated ${suggestions.length} suggestions`);
    
    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
      },
    });
    
  } catch (error) {
    console.error('❌ TinderOP Helper analysis failed:', error);
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    return createErrorResponse(errorMessage, 500, startTime);
  }
};

// Validate incoming request
function validateRequest(body: any): { valid: boolean; error?: string } {
  if (!body) {
    return { valid: false, error: 'Request body is required' };
  }
  
  if (!body.screenshot || typeof body.screenshot !== 'string') {
    return { valid: false, error: 'Screenshot is required and must be a base64 string' };
  }
  
  if (!body.context || typeof body.context !== 'object') {
    return { valid: false, error: 'Context object is required' };
  }
  
  // Basic image validation (check if it looks like base64)
  if (!isValidBase64(body.screenshot)) {
    return { valid: false, error: 'Invalid screenshot format' };
  }
  
  return { valid: true };
}

// Check if string is valid base64
function isValidBase64(str: string): boolean {
  try {
    return btoa(atob(str)) === str;
  } catch {
    return false;
  }
}

// Main AI analysis function
async function analyzeConversationWithAI(
  request: AnalyzeRequest, 
  apiKey: string
): Promise<ConversationSuggestion[]> {
  
  const { screenshot, context } = request;
  
  // Create analysis prompt
  const prompt = createAnalysisPrompt(context);
  
  console.log('🤖 Sending request to Gemini 2.5 Flash via OpenRouter');
  
  try {
    // Call Gemini 2.5 Flash via OpenRouter
    const result = await generateText({
      model: openrouter('google/gemini-2.0-flash-exp:free'),
      messages: [
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: prompt
            },
            {
              type: 'image',
              image: `data:image/png;base64,${screenshot}`
            }
          ]
        }
      ],
      temperature: 0.7,
      maxTokens: 1000,
    });
    
    // Parse AI response and extract suggestions
    const suggestions = parseAIResponse(result.text);
    
    console.log(`🎯 Generated ${suggestions.length} conversation suggestions`);
    return suggestions;
    
  } catch (error) {
    console.error('❌ AI analysis failed:', error);
    // Return fallback suggestions
    return generateFallbackSuggestions(context);
  }
}

// Create analysis prompt for AI
function createAnalysisPrompt(context: any): string {
  const { platform, messages } = context;
  
  return `You are an expert dating conversation coach. Analyze this ${platform} conversation screenshot and recent messages to provide 3-4 engaging reply suggestions.

Recent conversation context:
${messages && messages.length > 0 ? messages.join('\n') : 'No text context available - analyze the screenshot.'}

Instructions:
1. Analyze the conversation tone, context, and the person's communication style
2. Generate 3-4 different reply options with varying tones:
   - One witty/humorous response
   - One sincere/genuine response  
   - One flirty/playful response (if appropriate)
   - One thoughtful/question-based response

3. Each suggestion should be:
   - Natural and conversational
   - Appropriate for the context
   - Likely to continue the conversation
   - Under 200 characters
   - Platform-appropriate for ${platform}

4. Respond in this exact JSON format:
{
  "suggestions": [
    {
      "text": "Your reply suggestion here",
      "tone": "witty|sincere|flirty|thoughtful",
      "confidence": 0.85,
      "reasoning": "Brief explanation why this works"
    }
  ]
}

Focus on being helpful, appropriate, and encouraging genuine connection. Avoid pickup lines or overly aggressive approaches.`;
}

// Parse AI response and extract suggestions
function parseAIResponse(aiResponse: string): ConversationSuggestion[] {
  try {
    // Try to parse JSON response
    const cleaned = aiResponse.replace(/```json\n?|```\n?/g, '').trim();
    const parsed = JSON.parse(cleaned);
    
    if (parsed.suggestions && Array.isArray(parsed.suggestions)) {
      return parsed.suggestions.map((s: any) => ({
        text: s.text || '',
        tone: s.tone || 'casual',
        confidence: s.confidence || 0.7,
        reasoning: s.reasoning || ''
      }));
    }
  } catch (error) {
    console.warn('⚠️ Failed to parse AI response as JSON, using fallback extraction');
  }
  
  // Fallback: Extract suggestions from text
  return extractSuggestionsFromText(aiResponse);
}

// Fallback function to extract suggestions from unstructured text
function extractSuggestionsFromText(text: string): ConversationSuggestion[] {
  const suggestions: ConversationSuggestion[] = [];
  
  // Look for common patterns in AI responses
  const patterns = [
    /(?:suggestion|option|reply|response)[\s\d:.-]*(.+?)(?:\n|$)/gi,
    /"([^"]+)"/g,
    /^\d+\.\s*(.+?)(?:\n|$)/gm
  ];
  
  for (const pattern of patterns) {
    const matches = text.matchAll(pattern);
    for (const match of matches) {
      const suggestion = match[1]?.trim();
      if (suggestion && suggestion.length > 10 && suggestion.length < 300) {
        suggestions.push({
          text: suggestion,
          tone: 'casual',
          confidence: 0.6
        });
        
        if (suggestions.length >= 4) break;
      }
    }
    if (suggestions.length >= 4) break;
  }
  
  return suggestions.slice(0, 4);
}

// Generate fallback suggestions when AI fails
function generateFallbackSuggestions(context: any): ConversationSuggestion[] {
  const platform = context.platform || 'dating app';
  
  return [
    {
      text: "That's really interesting! Tell me more about that 😊",
      tone: 'sincere',
      confidence: 0.7
    },
    {
      text: "Haha, I love your sense of humor! What's your take on...",
      tone: 'witty', 
      confidence: 0.6
    },
    {
      text: "I'm curious - what's something you're passionate about lately?",
      tone: 'thoughtful',
      confidence: 0.8
    },
    {
      text: "You seem like someone with great stories. What's your latest adventure?",
      tone: 'flirty',
      confidence: 0.5
    }
  ];
}

// Generate conversation analysis
async function generateConversationAnalysis(
  request: AnalyzeRequest,
  apiKey: string
): Promise<any> {
  // For now, return basic analysis
  // This could be enhanced with additional AI calls
  return {
    conversationTone: 'friendly',
    userPersonality: 'engaging',
    recommendedApproach: 'continue building rapport with genuine interest'
  };
}

// Create error response
function createErrorResponse(message: string, status: number, startTime: number): Response {
  const response: AnalyzeResponse = {
    success: false,
    suggestions: [],
    processingTime: Date.now() - startTime,
    error: message
  };
  
  return new Response(JSON.stringify(response), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders,
    },
  });
}

// Health check endpoint
export const onRequestGet: PagesFunction = async () => {
  return new Response(JSON.stringify({ 
    status: 'healthy', 
    service: 'TinderOP Helper API',
    timestamp: new Date().toISOString()
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders,
    },
  });
};