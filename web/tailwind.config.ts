import type { Config } from "tailwindcss"

const config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "24px",
      screens: {
        "2xl": "1120px",
      },
    },
    extend: {
      colors: {
        // === BRAND COLORS ===
        "flame-red": "hsl(var(--flame-red))",
        "sparks-pink": "hsl(var(--sparks-pink))",
        "graphite-90": "hsl(var(--graphite-90))",
        "graphite-60": "hsl(var(--graphite-60))",
        "cloud-white": "hsl(var(--cloud-white))",

        // === SEMANTIC COLORS ===
        "success-green": "hsl(var(--success-green))",
        "warning-amber": "hsl(var(--warning-amber))",
        "error-crimson": "hsl(var(--error-crimson))",
        "info-blue": "hsl(var(--info-blue))",

        // === SYSTEM COLORS ===
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      // === FONT FAMILIES ===
      fontFamily: {
        sans: ["var(--font-family-primary)", "Inter", "system-ui", "sans-serif"],
        mono: ["var(--font-family-mono)", "JetBrains Mono", "monospace"],
      },

      // === BACKGROUND IMAGES ===
      backgroundImage: {
        "gradient-primary": "linear-gradient(135deg, hsl(var(--flame-red)) 0%, hsl(var(--sparks-pink)) 100%)",
        "gradient-hero": "linear-gradient(180deg, #FFF2F1 0%, #FFFFFF 60%)",
      },

      // === TYPOGRAPHY SCALE ===
      fontSize: {
        // Display sizes
        "display-1": ["3.5rem", { lineHeight: "4rem", letterSpacing: "-0.01em", fontWeight: "700" }],
        "display-1-mobile": ["2.5rem", { lineHeight: "3rem", letterSpacing: "-0.01em", fontWeight: "700" }],

        // Heading sizes
        h1: ["2.5rem", { lineHeight: "3rem", letterSpacing: "-0.01em", fontWeight: "700" }],
        "h1-mobile": ["2rem", { lineHeight: "2.5rem", letterSpacing: "-0.01em", fontWeight: "700" }],
        h2: ["2rem", { lineHeight: "2.5rem", letterSpacing: "-0.01em", fontWeight: "600" }],
        "h2-mobile": ["1.5rem", { lineHeight: "2rem", letterSpacing: "-0.01em", fontWeight: "600" }],
        h3: ["1.5rem", { lineHeight: "2rem", fontWeight: "600" }],
        h4: ["1.25rem", { lineHeight: "1.75rem", fontWeight: "600" }],
        h5: ["1.125rem", { lineHeight: "1.625rem", fontWeight: "600" }],
        h6: ["1rem", { lineHeight: "1.5rem", fontWeight: "600" }],

        // Body text
        "body-lg": ["1.125rem", { lineHeight: "1.75rem", fontWeight: "400" }],
        "body-md": ["1rem", { lineHeight: "1.5rem", fontWeight: "400" }],
        "body-sm": ["0.875rem", { lineHeight: "1.25rem", fontWeight: "400" }],
        caption: ["0.75rem", { lineHeight: "1rem", fontWeight: "400" }],
      },

      // === SPACING SCALE ===
      spacing: {
        xs: "var(--space-xs)",
        sm: "var(--space-sm)",
        md: "var(--space-md)",
        lg: "var(--space-lg)",
        xl: "var(--space-xl)",
        "2xl": "var(--space-2xl)",
        "3xl": "var(--space-3xl)",
        "4xl": "var(--space-4xl)",
      },

      // === BORDER RADIUS ===
      borderRadius: {
        sm: "var(--radius-sm)",
        md: "var(--radius-md)",
        lg: "var(--radius-lg)",
        xl: "var(--radius-xl)",
        full: "var(--radius-full)",
      },

      // === SHADOWS ===
      boxShadow: {
        sm: "var(--shadow-sm)",
        md: "var(--shadow-md)",
        lg: "var(--shadow-lg)",
        "button-primary": "var(--shadow-button-primary)",
      },
      letterSpacing: {
        caps: "0.04em",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config

export default config
