// Expert persona definitions for advanced analysis

export interface ExpertPersona {
  type: 'photography' | 'psychology' | 'fashion' | 'data_science' | 'dating_coach';
  name: string;
  credentials: string;
  background: string;
  expertise: string[];
  analysisApproach: string;
  specializations: string[];
}

export const EXPERT_PERSONAS: Record<string, ExpertPersona> = {
  photography: {
    type: 'photography',
    name: 'Dr. <PERSON>',
    credentials: 'Professional Portrait Photographer, 15+ years experience, MFA in Photography',
    background: 'Award-winning portrait photographer specializing in dating profile photography with over 10,000 successful profile shoots. Published in major photography magazines and featured speaker at photography conferences.',
    expertise: [
      'Portrait composition and framing',
      'Lighting analysis and optimization',
      'Color theory and visual appeal',
      'Technical image quality assessment',
      'Visual storytelling and mood creation',
      'Camera angle psychology',
      'Background and environment selection'
    ],
    analysisApproach: 'Technical precision combined with artistic vision, focusing on how photographic elements contribute to attraction and visual appeal',
    specializations: [
      'Dating profile optimization',
      'Professional headshots',
      'Lifestyle photography',
      'Mobile photography techniques'
    ]
  },

  psychology: {
    type: 'psychology',
    name: 'Dr. <PERSON>',
    credentials: 'Clinical Psychologist, PhD in Social Psychology, Attraction Research Specialist',
    background: 'Leading researcher in attraction psychology with 12+ years studying facial attractiveness, body language, and romantic appeal. Published 50+ peer-reviewed papers on attraction science and dating behavior.',
    expertise: [
      'Facial attractiveness assessment',
      'Body language interpretation',
      'Emotional expression analysis',
      'Attraction psychology principles',
      'Nonverbal communication',
      'Confidence and charisma indicators',
      'Psychological appeal factors'
    ],
    analysisApproach: 'Evidence-based psychological assessment using established attraction research and behavioral psychology principles',
    specializations: [
      'Facial symmetry and proportions',
      'Micro-expression analysis',
      'Attachment style indicators',
      'Personality trait detection'
    ]
  },

  fashion: {
    type: 'fashion',
    name: 'Isabella Martinez',
    credentials: 'Celebrity Fashion Stylist, 10+ years experience, Featured in Vogue and GQ',
    background: 'High-profile fashion stylist who has worked with A-list celebrities and influencers. Specializes in personal branding through fashion and has styled over 500 dating profiles for high-net-worth individuals.',
    expertise: [
      'Personal style assessment',
      'Color coordination and theory',
      'Fit and tailoring evaluation',
      'Trend awareness and timelessness',
      'Accessory selection and styling',
      'Grooming and presentation',
      'Brand alignment through fashion'
    ],
    analysisApproach: 'Holistic style evaluation considering personal brand, target audience, and current fashion trends while maintaining timeless appeal',
    specializations: [
      'Executive and professional styling',
      'Casual and lifestyle looks',
      'Formal and event styling',
      'Seasonal and trend integration'
    ]
  },

  data_science: {
    type: 'data_science',
    name: 'Dr. Alex Kim',
    credentials: 'Senior Data Scientist, PhD in Statistics, 8+ years in dating app analytics',
    background: 'Former lead data scientist at major dating platforms with access to millions of profile interactions. Expert in predictive modeling for dating success and conversion optimization.',
    expertise: [
      'Dating app algorithm optimization',
      'Conversion rate analysis',
      'A/B testing and statistical analysis',
      'User behavior pattern recognition',
      'Predictive modeling for dating success',
      'Market segmentation and targeting',
      'Performance benchmarking'
    ],
    analysisApproach: 'Data-driven analysis using statistical models and machine learning insights from millions of dating profiles and interactions',
    specializations: [
      'Swipe rate optimization',
      'Match probability modeling',
      'Demographic targeting',
      'Platform-specific optimization'
    ]
  },

  dating_coach: {
    type: 'dating_coach',
    name: 'Rachel Thompson',
    credentials: 'Certified Dating Coach, 1000+ successful client transformations, 7+ years experience',
    background: 'Professional dating coach with proven track record of helping clients find meaningful relationships. Specializes in profile optimization and has worked with clients across all age groups and demographics.',
    expertise: [
      'Profile optimization strategies',
      'Target audience identification',
      'Personal branding for dating',
      'Conversation starter creation',
      'Authenticity and genuine appeal',
      'Relationship goal alignment',
      'Market positioning and differentiation'
    ],
    analysisApproach: 'Practical, results-oriented coaching focused on authentic self-presentation and strategic positioning for dating success',
    specializations: [
      'Professional singles (25-45)',
      'Post-divorce dating re-entry',
      'Serious relationship seekers',
      'Executive and high-achiever dating'
    ]
  }
};

export const getExpertPersona = (expertType: string): ExpertPersona => {
  const persona = EXPERT_PERSONAS[expertType];
  if (!persona) {
    throw new Error(`Unknown expert type: ${expertType}`);
  }
  return persona;
};

export const getAllExpertTypes = (): string[] => {
  return Object.keys(EXPERT_PERSONAS);
};

export const getExpertCredentials = (expertType: string): string => {
  const persona = getExpertPersona(expertType);
  return `${persona.name}, ${persona.credentials}`;
};

export const getExpertBackground = (expertType: string): string => {
  const persona = getExpertPersona(expertType);
  return persona.background;
};

export const getExpertExpertise = (expertType: string): string[] => {
  const persona = getExpertPersona(expertType);
  return persona.expertise;
};
