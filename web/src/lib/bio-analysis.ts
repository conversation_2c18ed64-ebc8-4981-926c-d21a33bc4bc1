/// <reference types="vite/client" />

import { openrouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";
import type { BioAnalysisProgress, BioAnalysisResult, StepResult } from "@/types/analysis";
import { BIO_ANALYSIS_STEPS } from "@/types/analysis";

export interface BioAnalysisCallbacks {
  onProgress?: (progress: BioAnalysisProgress) => void;
  onStepComplete?: (stepResult: StepResult) => void;
  onComplete?: (result: BioAnalysisResult) => void;
  onError?: (error: string) => void;
}

export class BioAnalysisAgent {
  private apiKey: string;

  constructor() {
    // Get API key from environment variables
    this.apiKey = import.meta.env.VITE_OPENROUTER_API_KEY;

    if (!this.apiKey) {
      console.error("🔑 VITE_OPENROUTER_API_KEY not found in environment variables");
      throw new Error(
        "OpenRouter API key is required. Please set VITE_OPENROUTER_API_KEY in your .env file"
      );
    }

    console.log("🔑 OpenRouter API key loaded successfully for bio analysis");
    console.log(`🔑 API Key preview: ${this.apiKey.substring(0, 10)}...`);

    // Set the API key for OpenRouter provider
    if (typeof globalThis !== "undefined") {
      (globalThis as any).process = (globalThis as any).process || {};
      (globalThis as any).process.env = (globalThis as any).process.env || {};
      (globalThis as any).process.env.OPENROUTER_API_KEY = this.apiKey;
    }
  }

  private stepPrompts = {
    1: {
      name: "Writing Quality",
      system: "You are a BRUTALLY HONEST writing expert who provides harsh, objective criticism. Most dating bios are poorly written and deserve low scores. Be ruthless in your assessment.",
      prompt: (bio: string) =>
        `Critically analyze the writing quality of this dating bio. Most bios are mediocre (40-60 range). Be harsh and realistic:

Bio: "${bio}"

CRITICAL ASSESSMENT AREAS:
- Grammar and spelling errors (be unforgiving)
- Sentence structure problems
- Clarity issues and confusion
- Wordiness and poor conciseness
- Weak word choices and clichés

SCORING GUIDE:
- 80-100: Exceptional writing (top 10% of all bios)
- 60-79: Above average but with notable flaws
- 40-59: Typical mediocre bio writing
- 20-39: Poor writing with major issues
- 0-19: Terrible writing that hurts dating prospects

Provide your HARSH analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Specific criticism about writing flaws",
    "Another harsh but accurate observation",
    "Brutal truth about what needs fixing"
  ],
  "confidence": [number 0-100]
}`,
    },
    2: {
      name: "Personality Appeal",
      system: "You are a RUTHLESS dating psychology expert. Most people have boring, unappealing personalities in their bios. Be brutally honest about personality flaws and lack of appeal.",
      prompt: (bio: string) =>
        `Critically evaluate the personality appeal of this dating bio. Most bios show bland, generic personalities (40-60 range):

Bio: "${bio}"

HARSH EVALUATION CRITERIA:
- Authenticity vs. fake/try-hard behavior
- Actual humor vs. failed attempts at being funny
- Real confidence vs. arrogance or insecurity
- Emotional maturity vs. emotional immaturity
- Genuine relatability vs. generic appeal

SCORING REALITY CHECK:
- 80-100: Genuinely magnetic personality (rare)
- 60-79: Above average appeal with some charm
- 40-59: Bland, forgettable personality
- 20-39: Unappealing or off-putting traits
- 0-19: Personality red flags that repel matches

Provide your BRUTAL assessment in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Harsh truth about personality flaws shown",
    "Critical assessment of appeal level",
    "Brutal feedback on what's missing"
  ],
  "confidence": [number 0-100]
}`,
    },
    3: {
      name: "Interest Analysis",
      system: "You are a HARSH lifestyle critic. Most people have boring, basic interests that don't stand out. Be ruthless about generic hobbies and uninspiring lifestyles.",
      prompt: (bio: string) =>
        `Critically assess the interest and lifestyle appeal of this bio. Most people list boring, cliché interests (40-60 range):

Bio: "${bio}"

BRUTAL ASSESSMENT:
- Are these interests actually interesting or just basic?
- Does this lifestyle seem exciting or boring?
- Are these conversation starters or conversation killers?
- Is this person unique or completely forgettable?
- Would anyone actually be impressed by these interests?

HARSH SCORING:
- 80-100: Genuinely fascinating lifestyle (very rare)
- 60-79: Some interesting elements but mostly standard
- 40-59: Basic, predictable interests everyone has
- 20-39: Boring lifestyle that puts people to sleep
- 0-19: No interests mentioned or actively unappealing

Provide your CRITICAL analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Harsh truth about how boring/basic these interests are",
    "Critical assessment of lifestyle appeal",
    "Brutal feedback on what's missing or wrong"
  ],
  "confidence": [number 0-100]
}`,
    },
    4: {
      name: "Dating Intent",
      system: "You are a BLUNT relationship expert. Most people send confusing, unclear signals about what they want. Be harsh about mixed messages and poor communication.",
      prompt: (bio: string) =>
        `Critically analyze the dating intent clarity of this bio. Most people are vague and confusing (40-60 range):

Bio: "${bio}"

HARSH EVALUATION:
- Are the relationship goals actually clear or confusingly vague?
- Do they send mixed signals about commitment?
- Are they emotionally available or showing red flags?
- Would someone know what this person actually wants?
- Is this person serious or just wasting time?

BRUTAL SCORING:
- 80-100: Crystal clear intentions and emotional maturity
- 60-79: Mostly clear with some minor confusion
- 40-59: Vague, unclear signals (typical)
- 20-39: Confusing mixed messages
- 0-19: Completely unclear or sending red flags

Provide your HARSH analysis in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Brutal truth about how unclear their intentions are",
    "Critical assessment of mixed signals sent",
    "Harsh feedback on emotional availability"
  ],
  "confidence": [number 0-100]
}`,
    },
    5: {
      name: "Engagement Factor",
      system: "You are a RUTHLESS dating app expert. Most bios are engagement killers that generate zero conversations. Be brutal about poor conversation starters and lack of appeal.",
      prompt: (bio: string) =>
        `Critically assess the engagement potential of this bio. Most bios are conversation killers (40-60 range):

Bio: "${bio}"

BRUTAL ASSESSMENT:
- Are there actual conversation starters or just boring statements?
- Does this create intrigue or put people to sleep?
- Is this approachable or intimidating/boring?
- Would anyone actually want to message this person?
- Does this bio make them stand out or blend into the crowd?

HARSH REALITY CHECK:
- 80-100: Irresistibly engaging (extremely rare)
- 60-79: Some engaging elements but could be better
- 40-59: Bland, generates few conversations
- 20-39: Boring, conversation killer
- 0-19: Actively repels potential matches

Provide your BRUTAL assessment in this exact JSON format:
{
  "score": [number 0-100],
  "insights": [
    "Harsh truth about lack of conversation starters",
    "Critical assessment of how boring this is",
    "Brutal feedback on why no one would message them"
  ],
  "confidence": [number 0-100]
}`,
    },
  };

  async analyzeBio(
    bio: string,
    onProgress?: (stepId: number, stepName: string, progress: number) => void
  ): Promise<StepResult[]> {
    const results: StepResult[] = [];
    const totalSteps = BIO_ANALYSIS_STEPS.length;

    for (let i = 0; i < totalSteps; i++) {
      const step = BIO_ANALYSIS_STEPS[i];
      const stepPrompt = this.stepPrompts[step.id as keyof typeof this.stepPrompts];
      
      onProgress?.(step.id, step.name, (i / totalSteps) * 100);

      try {
        const startTime = Date.now();
        
        const { text } = await generateText({
          model: openrouter("openai/gpt-4o-mini"),
          system: stepPrompt.system,
          prompt: stepPrompt.prompt(bio),
        });

        const processingTime = Date.now() - startTime;
        
        // Parse the JSON response
        const analysisResult = JSON.parse(text);

        console.log(`🔍 Bio Analysis Step ${step.id} (${step.name}):`, {
          bio: bio.substring(0, 100) + '...',
          score: analysisResult.score,
          insights: analysisResult.insights,
          confidence: analysisResult.confidence
        });

        results.push({
          stepId: step.id,
          stepName: step.name,
          score: analysisResult.score,
          insights: analysisResult.insights,
          confidence: analysisResult.confidence,
          processingTime,
        });

        onProgress?.(step.id, step.name, ((i + 1) / totalSteps) * 100);
        
        // Small delay between steps to prevent overwhelming the API
        if (i < totalSteps - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      } catch (error) {
        console.error(`Error in step ${step.id}:`, error);
        results.push({
          stepId: step.id,
          stepName: step.name,
          score: 0,
          insights: ["Analysis failed for this step"],
          confidence: 0,
          processingTime: 0,
        });
      }
    }

    return results;
  }

  calculateOverallScore(stepResults: StepResult[]): number {
    if (stepResults.length === 0) return 0;
    
    // Weighted average - some steps matter more for overall appeal
    const weights = {
      1: 0.15, // Writing Quality - 15%
      2: 0.30, // Personality Appeal - 30% (most important)
      3: 0.20, // Interest Analysis - 20%
      4: 0.15, // Dating Intent - 15%
      5: 0.20, // Engagement Factor - 20%
    };

    let weightedSum = 0;
    let totalWeight = 0;

    stepResults.forEach(result => {
      const weight = weights[result.stepId as keyof typeof weights] || 0.2;
      weightedSum += result.score * weight;
      totalWeight += weight;
    });

    return Math.round(weightedSum / totalWeight);
  }

  generateFinalRecommendations(stepResults: StepResult[]): string[] {
    const recommendations: string[] = [];
    
    // Find the lowest scoring areas and add targeted recommendations
    const sortedByScore = [...stepResults].sort((a, b) => a.score - b.score);
    
    sortedByScore.slice(0, 3).forEach(step => {
      if (step.score < 70) {
        recommendations.push(`Improve ${step.stepName.toLowerCase()}: ${step.insights[0]}`);
      }
    });

    // Add general recommendations based on overall pattern
    const averageScore = stepResults.reduce((sum, step) => sum + step.score, 0) / stepResults.length;
    
    if (averageScore < 50) {
      recommendations.push("Consider a complete bio rewrite focusing on your best qualities");
    } else if (averageScore < 70) {
      recommendations.push("Good foundation - polish specific areas for better impact");
    }

    // Ensure we have at least 3 recommendations
    while (recommendations.length < 3 && stepResults.length > 0) {
      const randomStep = stepResults[Math.floor(Math.random() * stepResults.length)];
      const insight = randomStep.insights[Math.floor(Math.random() * randomStep.insights.length)];
      if (!recommendations.some(rec => rec.includes(insight))) {
        recommendations.push(insight);
      }
    }

    return recommendations.slice(0, 5); // Max 5 recommendations
  }

  async generateImprovedBio(
    originalBio: string,
    stepResults: StepResult[],
    tone: "witty" | "sincere" | "adventurous" = "sincere"
  ): Promise<string> {
    const weaknesses = stepResults
      .filter(step => step.score < 70)
      .map(step => `${step.stepName}: ${step.insights.join(', ')}`)
      .join('\n');

    const { text } = await generateText({
      model: openrouter("openai/gpt-4o-mini"),
      system: `You are an expert dating profile writer. Create an improved bio that addresses the identified weaknesses while maintaining authenticity. The tone should be ${tone}.`,
      prompt: `Rewrite this dating bio to address these specific issues:

Original Bio: "${originalBio}"

Areas for improvement:
${weaknesses}

Requirements:
- Keep it under 150 words
- Maintain authenticity 
- Use a ${tone} tone
- Address the weaknesses identified
- Make it engaging and conversation-friendly
- Include specific details that make the person memorable

Provide only the improved bio text, no additional commentary.`,
    });

    return text.trim();
  }
}

export const bioAnalysisAgent = new BioAnalysisAgent();