// TinderOP Helper - Conversation Analysis Types
// Type definitions for conversation analysis features

export type Platform = 'tinder' | 'bumble' | 'hinge' | 'unknown';

export type SuggestionTone = 'witty' | 'sincere' | 'flirty' | 'casual' | 'thoughtful';

export type AnalysisPhase = 'capture' | 'upload' | 'analysis' | 'suggestions' | 'complete';

// Context from the dating app conversation
export interface ConversationContext {
  platform: Platform;
  url: string;
  timestamp: number;
  messages: string[];
  userAgent?: string;
  metadata?: {
    chatId?: string;
    matchId?: string;
    conversationLength?: number;
    lastMessageTime?: number;
  };
}

// Individual conversation suggestion
export interface ConversationSuggestion {
  text: string;
  tone: SuggestionTone;
  confidence: number; // 0-1
  reasoning?: string;
  category?: 'opener' | 'response' | 'question' | 'closer';
  tags?: string[];
}

// Analysis of the conversation and user
export interface ConversationAnalysis {
  conversationTone: string;
  userPersonality: string;
  recommendedApproach: string;
  confidence: number; // 0-1
  insights?: {
    communicationStyle: string;
    interests: string[];
    engagementLevel: 'high' | 'medium' | 'low';
    conversationStage: 'initial' | 'building' | 'established' | 'advanced';
  };
}

// Progress tracking for analysis
export interface ConversationAnalysisProgress {
  phase: AnalysisPhase;
  progress: number; // 0-100
  message: string;
  timestamp?: number;
}

// Complete analysis result
export interface ConversationAnalysisResult {
  suggestions: ConversationSuggestion[];
  analysis: ConversationAnalysis;
  processingTime: number;
  platform: Platform;
  timestamp: number;
  success: boolean;
  error?: string;
  metadata?: {
    method: 'direct' | 'api';
    modelUsed: string;
    tokensUsed?: number;
    retryCount?: number;
  };
}

// Configuration for analysis service
export interface ConversationAnalysisConfig {
  onProgress?: (progress: ConversationAnalysisProgress) => void;
  onComplete?: (result: ConversationAnalysisResult) => void;
  onError?: (error: string) => void;
  apiUrl?: string;
  maxRetries?: number;
  timeout?: number;
  preferredMethod?: 'direct' | 'api' | 'auto';
  customPrompt?: string;
}

// API request/response types
export interface AnalyzeConversationRequest {
  screenshot: string; // Base64 encoded
  context: ConversationContext;
  timestamp: number;
  config?: {
    includeAnalysis?: boolean;
    maxSuggestions?: number;
    preferredTones?: SuggestionTone[];
  };
}

export interface AnalyzeConversationResponse {
  success: boolean;
  suggestions: ConversationSuggestion[];
  analysis?: ConversationAnalysis;
  processingTime: number;
  error?: string;
  metadata?: {
    modelUsed: string;
    tokensUsed?: number;
    requestId: string;
  };
}

// Health check types
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  service: string;
  version: string;
  timestamp: string;
  checks: {
    api: boolean;
    ai: boolean;
    environment: boolean;
  };
}

// Extension communication types
export interface ExtensionMessage {
  action: 'captureScreenshot' | 'analyzeConversation' | 'getConfig' | 'updateConfig';
  data?: any;
  timestamp: number;
}

export interface ExtensionResponse {
  success: boolean;
  data?: any;
  error?: string;
  timestamp: number;
}

// Storage types for caching/history
export interface ConversationAnalysisHistory {
  id: string;
  platform: Platform;
  timestamp: number;
  suggestions: ConversationSuggestion[];
  analysis: ConversationAnalysis;
  success: boolean;
  processingTime: number;
}

export interface ConversationAnalysisCache {
  [key: string]: {
    result: ConversationAnalysisResult;
    timestamp: number;
    expiresAt: number;
  };
}

// Error types
export interface ConversationAnalysisError extends Error {
  code: 'INVALID_INPUT' | 'API_ERROR' | 'AI_ERROR' | 'NETWORK_ERROR' | 'TIMEOUT' | 'RATE_LIMITED';
  details?: any;
  retryable: boolean;
}

// Platform-specific types
export interface PlatformConfig {
  platform: Platform;
  messageSelectors: string[];
  inputSelectors: string[];
  chatAreaSelectors: string[];
  specialHandling?: {
    waitForLoad?: number;
    scrollToBottom?: boolean;
    hideElements?: string[];
  };
}

// UI state types for React components
export interface ConversationAnalysisState {
  isAnalyzing: boolean;
  currentPhase: AnalysisPhase | null;
  progress: number;
  lastResult: ConversationAnalysisResult | null;
  error: string | null;
  history: ConversationAnalysisHistory[];
}

// Hook return type
export interface UseConversationAnalysis {
  analyzeConversation: (screenshot: string, context: ConversationContext, config?: ConversationAnalysisConfig) => Promise<ConversationAnalysisResult>;
  isAnalyzing: boolean;
  progress: ConversationAnalysisProgress | null;
  lastResult: ConversationAnalysisResult | null;
  error: string | null;
  clearError: () => void;
  testConnection: (apiUrl?: string) => Promise<boolean>;
}