import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

export function DesignSystemShowcase() {
  const [darkMode, setDarkMode] = useState(false);

  const toggleTheme = () => {
    setDarkMode(!darkMode);
    document.documentElement.classList.toggle('dark');
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container-section">
          <div className="flex items-center justify-between py-4">
            <div>
              <h1 className="text-h1 font-bold text-foreground">TinderOP Design System</h1>
              <p className="text-body text-muted-foreground">Component library and style guide</p>
            </div>
            <div className="flex items-center space-x-2">
              <Label htmlFor="theme-toggle">Dark Mode</Label>
              <Switch
                id="theme-toggle"
                checked={darkMode}
                onCheckedChange={toggleTheme}
              />
            </div>
          </div>
        </div>
      </header>

      <main className="container-section space-section">
        {/* Color Palette */}
        <section className="space-content">
          <div className="space-items">
            <h2 className="text-heading-2">Color Palette</h2>
            <p className="text-body-large">Our brand colors and semantic color system</p>
          </div>
          
          <div className="responsive-grid-3">
            {/* Brand Colors */}
            <Card>
              <CardHeader>
                <CardTitle>Brand Colors</CardTitle>
                <CardDescription>Primary brand identity colors</CardDescription>
              </CardHeader>
              <CardContent className="space-items">
                <div className="space-tight">
                  <div className="h-16 w-full rounded-lg bg-flame-red"></div>
                  <p className="text-body-sm font-medium">Flame Red</p>
                  <p className="text-caption text-muted-foreground">#FF5851</p>
                </div>
                <div className="space-tight">
                  <div className="h-16 w-full rounded-lg bg-sparks-pink"></div>
                  <p className="text-body-sm font-medium">Sparks Pink</p>
                  <p className="text-caption text-muted-foreground">#FF8A80</p>
                </div>
              </CardContent>
            </Card>

            {/* Neutral Colors */}
            <Card>
              <CardHeader>
                <CardTitle>Neutral Colors</CardTitle>
                <CardDescription>Text and background colors</CardDescription>
              </CardHeader>
              <CardContent className="space-items">
                <div className="space-tight">
                  <div className="h-16 w-full rounded-lg bg-graphite-90"></div>
                  <p className="text-body-sm font-medium">Graphite 90</p>
                  <p className="text-caption text-muted-foreground">#1A1A1A</p>
                </div>
                <div className="space-tight">
                  <div className="h-16 w-full rounded-lg bg-graphite-60"></div>
                  <p className="text-body-sm font-medium">Graphite 60</p>
                  <p className="text-caption text-muted-foreground">#666666</p>
                </div>
              </CardContent>
            </Card>

            {/* Semantic Colors */}
            <Card>
              <CardHeader>
                <CardTitle>Semantic Colors</CardTitle>
                <CardDescription>Status and feedback colors</CardDescription>
              </CardHeader>
              <CardContent className="space-items">
                <div className="space-tight">
                  <div className="h-12 w-full rounded-lg bg-success-green"></div>
                  <p className="text-body-sm font-medium">Success</p>
                </div>
                <div className="space-tight">
                  <div className="h-12 w-full rounded-lg bg-warning-amber"></div>
                  <p className="text-body-sm font-medium">Warning</p>
                </div>
                <div className="space-tight">
                  <div className="h-12 w-full rounded-lg bg-error-crimson"></div>
                  <p className="text-body-sm font-medium">Error</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        <Separator />

        {/* Typography */}
        <section className="space-content">
          <div className="space-items">
            <h2 className="text-heading-2">Typography</h2>
            <p className="text-body-large">Consistent typography scale and hierarchy</p>
          </div>
          
          <Card>
            <CardContent className="space-content">
              <div className="space-items">
                <h1 className="text-display-1">Display 1 - Hero Headlines</h1>
                <h1 className="text-h1">Heading 1 - Page Titles</h1>
                <h2 className="text-h2">Heading 2 - Section Titles</h2>
                <h3 className="text-h3">Heading 3 - Subsection Titles</h3>
                <h4 className="text-h4">Heading 4 - Component Titles</h4>
                <p className="text-body-lg">Body Large - Lead paragraphs and important content</p>
                <p className="text-body">Body Medium - Standard paragraph text and content</p>
                <p className="text-body-sm">Body Small - Secondary content and descriptions</p>
                <p className="text-caption">Caption - Labels, metadata, and fine print</p>
              </div>
            </CardContent>
          </Card>
        </section>

        <Separator />

        {/* Buttons */}
        <section className="space-content">
          <div className="space-items">
            <h2 className="text-heading-2">Buttons</h2>
            <p className="text-body-large">Interactive button components with consistent styling</p>
          </div>
          
          <div className="responsive-grid-2">
            <Card>
              <CardHeader>
                <CardTitle>Button Variants</CardTitle>
                <CardDescription>Different button styles for various use cases</CardDescription>
              </CardHeader>
              <CardContent className="space-items">
                <Button variant="primary">Primary Button</Button>
                <Button variant="secondary">Secondary Button</Button>
                <Button variant="tertiary">Tertiary Button</Button>
                <Button variant="ghost">Ghost Button</Button>
                <Button variant="outline">Outline Button</Button>
                <Button variant="destructive">Destructive Button</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Button Sizes</CardTitle>
                <CardDescription>Different button sizes for various contexts</CardDescription>
              </CardHeader>
              <CardContent className="space-items">
                <Button size="sm">Small Button</Button>
                <Button size="default">Default Button</Button>
                <Button size="lg">Large Button</Button>
                <Button size="icon">🔥</Button>
              </CardContent>
            </Card>
          </div>
        </section>

        <Separator />

        {/* Form Elements */}
        <section className="space-content">
          <div className="space-items">
            <h2 className="text-heading-2">Form Elements</h2>
            <p className="text-body-large">Input fields and form controls</p>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Input Fields</CardTitle>
              <CardDescription>Text inputs and form controls</CardDescription>
            </CardHeader>
            <CardContent className="space-items">
              <div className="space-tight">
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" type="email" placeholder="Enter your email" />
              </div>
              <div className="space-tight">
                <Label htmlFor="password">Password</Label>
                <Input id="password" type="password" placeholder="Enter your password" />
              </div>
              <div className="space-tight">
                <Label htmlFor="disabled">Disabled Input</Label>
                <Input id="disabled" disabled placeholder="This input is disabled" />
              </div>
            </CardContent>
          </Card>
        </section>

        <Separator />

        {/* Status Badges */}
        <section className="space-content">
          <div className="space-items">
            <h2 className="text-heading-2">Status Indicators</h2>
            <p className="text-body-large">Badges and status indicators</p>
          </div>
          
          <Card>
            <CardContent className="space-items">
              <div className="flex flex-wrap gap-4">
                <Badge className="status-success">Success</Badge>
                <Badge className="status-warning">Warning</Badge>
                <Badge className="status-error">Error</Badge>
                <Badge className="status-info">Info</Badge>
              </div>
            </CardContent>
          </Card>
        </section>

        <Separator />

        {/* Cards */}
        <section className="space-content">
          <div className="space-items">
            <h2 className="text-heading-2">Cards</h2>
            <p className="text-body-large">Container components for grouping content</p>
          </div>
          
          <div className="responsive-grid-3">
            <Card>
              <CardHeader>
                <CardTitle>Basic Card</CardTitle>
                <CardDescription>A simple card with header and content</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-body">This is the card content area where you can place any content.</p>
              </CardContent>
            </Card>

            <Card className="card-elevated">
              <CardHeader>
                <CardTitle>Elevated Card</CardTitle>
                <CardDescription>Card with enhanced shadow on hover</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-body">This card has an elevated style with hover effects.</p>
              </CardContent>
              <CardFooter>
                <Button size="sm">Action</Button>
              </CardFooter>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Card with Footer</CardTitle>
                <CardDescription>Card including a footer section</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-body">Content area with footer actions below.</p>
              </CardContent>
              <CardFooter className="justify-between">
                <Button variant="ghost" size="sm">Cancel</Button>
                <Button size="sm">Confirm</Button>
              </CardFooter>
            </Card>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t border-border bg-muted/50 mt-16">
        <div className="container-section">
          <div className="py-8 text-center">
            <p className="text-body text-muted-foreground">
              TinderOP Design System - Built with Tailwind CSS and shadcn/ui
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
