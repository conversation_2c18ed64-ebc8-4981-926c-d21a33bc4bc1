import { ArrowLeft, Brain, Camera, Crown, FileUp, Loader2, Shield, Sparkles, TrendingUp, X } from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";
import { <PERSON> } from "react-router";
import { PrivacyNotice } from "@/components/PrivacyNotice";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AdvancedImageAnalyzer } from "@/lib/advanced/image-analyzer-pro";
import type { AdvancedImageAnalysisResult, AdvancedAnalysisProgress } from "@/lib/advanced/types/advanced-analysis";

interface ImageWithPreview {
  id: string;
  fileName: string;
  preview: string;
  file: File;
}

type AnalysisStatus = "idle" | "processing" | "done";

export default function ImageAnalyzerPro() {
  const [images, setImages] = useState<ImageWithPreview[]>([]);
  const [status, setStatus] = useState<AnalysisStatus>("idle");
  const [results, setResults] = useState<AdvancedImageAnalysisResult[]>([]);
  const [progress, setProgress] = useState<AdvancedAnalysisProgress | null>(null);
  const [analyzer] = useState(() => new AdvancedImageAnalyzer());

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newImages: ImageWithPreview[] = acceptedFiles.map((file) => ({
      id: Math.random().toString(36).substr(2, 9),
      fileName: file.name,
      preview: URL.createObjectURL(file),
      file,
    }));

    setImages((prev) => [...prev, ...newImages]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
    },
    maxFiles: 5,
  });

  const removeImage = (id: string) => {
    setImages((prev) => {
      const updated = prev.filter((img) => img.id !== id);
      const toRevoke = prev.find((img) => img.id === id);
      if (toRevoke) {
        URL.revokeObjectURL(toRevoke.preview);
      }
      return updated;
    });
  };

  const analyzeImages = async () => {
    if (images.length === 0) return;

    setStatus("processing");
    setResults([]);
    setProgress(null);

    try {
      for (const image of images) {
        // Convert image to base64
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        await new Promise((resolve) => {
          img.onload = resolve;
          img.src = image.preview;
        });

        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        const base64 = canvas.toDataURL('image/jpeg', 0.8).split(',')[1];

        const result = await analyzer.analyzeImage(
          base64,
          image.fileName,
          {
            analysisDepth: 'comprehensive',
            includeComparative: true,
            generateImprovements: true
          },
          (progressData) => {
            setProgress(progressData);
          }
        );

        setResults(prev => [...prev, result]);
      }

      setStatus("done");
    } catch (error) {
      console.error("Advanced analysis failed:", error);
      setStatus("idle");
    }
  };

  useEffect(() => {
    return () => {
      images.forEach((image) => URL.revokeObjectURL(image.preview));
    };
  }, [images]);

  return (
    <div className="min-h-screen bg-background">
      <header className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container-section">
          <div className="flex items-center justify-between py-4">
            <Button variant="ghost" size="sm" asChild>
              <Link to="/image-analyzer">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Basic
              </Link>
            </Button>
          </div>
        </div>
      </header>

      <main className="container-section">
        {status === "idle" && (
          <div className="max-w-2xl mx-auto">
            <div className="text-center mb-8">
              <h1 className="text-heading-1 mb-4">
                Image Analyzer Pro
              </h1>
              <p className="text-body-large">
                Advanced AI-powered photo analysis and optimization
              </p>
            </div>

            <div
              {...getRootProps()}
              className={`border-2 border-dashed border-border rounded-lg p-8 text-center transition-colors cursor-pointer ${
                isDragActive
                  ? "border-flame-red bg-flame-red/5"
                  : "hover:border-flame-red"
              }`}
            >
              <input {...getInputProps()} />
              <FileUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-h3 mb-2">
                Upload Your Photos
              </h3>
              <p className="text-body mb-4">
                Drag and drop your images here, or click to select files
              </p>
              <p className="text-caption text-muted-foreground">
                Supports JPEG, PNG, WebP • Max 5 files
              </p>
            </div>

            {images.length > 0 && (
              <div className="mt-8">
                <h3 className="text-lg font-semibold mb-4">Selected Images ({images.length})</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-6">
                  {images.map((image) => (
                    <div key={image.id} className="relative group">
                      <img
                        src={image.preview}
                        alt={image.fileName}
                        className="w-full h-32 object-cover rounded-lg border"
                      />
                      <button
                        onClick={() => removeImage(image.id)}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="h-4 w-4" />
                      </button>
                      <p className="text-xs text-gray-600 mt-1 truncate">
                        {image.fileName}
                      </p>
                    </div>
                  ))}
                </div>

                <div className="text-center">
                  <Button onClick={analyzeImages} size="lg" className="bg-gradient-to-r from-purple-600 to-blue-600">
                    <Camera className="mr-2 h-5 w-5" />
                    Start Advanced Analysis
                  </Button>
                </div>
              </div>
            )}

            <PrivacyNotice />
          </div>
        )}

        {status === "processing" && (
          <div className="max-w-2xl mx-auto text-center">
            <div className="mb-8">
              <Crown className="h-16 w-16 text-purple-600 mx-auto mb-4 animate-pulse" />
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Advanced Analysis in Progress
              </h2>
              <p className="text-gray-600">
                Our expert AI system is analyzing your photos with professional-grade precision
              </p>
            </div>

            {progress && (
              <div className="space-y-4">
                <div className="bg-white rounded-lg p-6 shadow-sm border">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium text-gray-900">
                      {progress.message}
                    </span>
                    <span className="text-sm text-gray-500">
                      {Math.round(progress.progress)}%
                    </span>
                  </div>
                  <Progress value={progress.progress} className="h-2" />
                  
                  {progress.currentExpert && (
                    <p className="text-sm text-gray-600 mt-2">
                      Current Expert: {progress.currentExpert}
                    </p>
                  )}
                  
                  {progress.estimatedTimeRemaining && (
                    <p className="text-xs text-gray-500 mt-1">
                      Estimated time remaining: {progress.estimatedTimeRemaining}s
                    </p>
                  )}
                </div>

                <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Processing with OpenRouter o3 model...</span>
                </div>
              </div>
            )}
          </div>
        )}

        {status === "done" && results.length > 0 && (
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-2">
                Advanced Analysis Complete
              </h2>
              <p className="text-gray-600">
                Professional insights from our expert AI system
              </p>
            </div>

            <div className="space-y-8">
              {results.map((result, index) => (
                <Card key={result.id} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center space-x-2">
                        <img
                          src={result.preview}
                          alt={result.fileName}
                          className="w-12 h-12 object-cover rounded-lg"
                        />
                        <span>{result.fileName}</span>
                      </CardTitle>
                      <div className="flex items-center space-x-4">
                        <Badge variant="outline" className="text-lg font-bold">
                          {result.overallScore}/100
                        </Badge>
                        <Badge variant="secondary">
                          {result.percentileRank}th percentile
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <Tabs defaultValue="overview" className="w-full">
                      <TabsList className="grid w-full grid-cols-5">
                        <TabsTrigger value="overview">Overview</TabsTrigger>
                        <TabsTrigger value="experts">Expert Analysis</TabsTrigger>
                        <TabsTrigger value="insights">Insights</TabsTrigger>
                        <TabsTrigger value="demographics">Demographics</TabsTrigger>
                        <TabsTrigger value="comparison">Comparison</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="overview" className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm">Overall Score</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-2xl font-bold text-purple-600">
                                {result.overallScore}/100
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm">Percentile Rank</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-2xl font-bold text-blue-600">
                                {result.percentileRank}th
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm">Improvement Potential</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-2xl font-bold text-green-600">
                                {result.improvementPotential}%
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm">Market Competitiveness</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-2xl font-bold text-orange-600">
                                {result.marketCompetitiveness}/100
                              </div>
                            </CardContent>
                          </Card>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">Quick Wins</CardTitle>
                              <CardDescription>Easy improvements with high impact</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                {result.quickWins.slice(0, 3).map((rec, i) => (
                                  <div key={i} className="flex items-start space-x-3">
                                    <Badge variant="outline" className="text-xs">
                                      {rec.impactScore}
                                    </Badge>
                                    <p className="text-sm">{rec.recommendation}</p>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader>
                              <CardTitle className="text-lg">Long-term Improvements</CardTitle>
                              <CardDescription>Strategic changes for maximum impact</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-3">
                                {result.longTermImprovements.slice(0, 3).map((rec, i) => (
                                  <div key={i} className="flex items-start space-x-3">
                                    <Badge variant="outline" className="text-xs">
                                      {rec.impactScore}
                                    </Badge>
                                    <p className="text-sm">{rec.recommendation}</p>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </TabsContent>

                      <TabsContent value="experts" className="space-y-4">
                        {result.expertAnalyses.map((expert, i) => (
                          <Card key={i}>
                            <CardHeader>
                              <div className="flex items-center justify-between">
                                <CardTitle className="text-lg capitalize">
                                  {expert.expertType.replace('_', ' ')} Expert
                                </CardTitle>
                                <div className="flex items-center space-x-2">
                                  <Badge variant="outline">{expert.score}/100</Badge>
                                  <Badge variant="secondary">{expert.confidence}% confident</Badge>
                                </div>
                              </div>
                              <CardDescription>{expert.credentials}</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <p className="text-sm text-gray-700 mb-4">{expert.analysis}</p>
                              <div className="space-y-2">
                                <h4 className="font-medium text-sm">Key Observations:</h4>
                                <ul className="text-sm text-gray-600 space-y-1">
                                  {expert.keyObservations.map((obs, j) => (
                                    <li key={j} className="flex items-start space-x-2">
                                      <span className="text-purple-500">•</span>
                                      <span>{obs}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </TabsContent>

                      <TabsContent value="insights" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <Card>
                            <CardHeader>
                              <CardTitle>All Recommendations</CardTitle>
                              <CardDescription>Prioritized by impact potential</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-4">
                                {result.actionableInsights.map((insight, i) => (
                                  <div key={i} className="border-l-4 border-purple-200 pl-4">
                                    <div className="flex items-center justify-between mb-2">
                                      <Badge
                                        variant={insight.priority === 'high' ? 'default' : 'secondary'}
                                        className="text-xs"
                                      >
                                        {insight.priority} priority
                                      </Badge>
                                      <span className="text-xs text-gray-500">
                                        Impact: {insight.impactScore}/100
                                      </span>
                                    </div>
                                    <p className="text-sm font-medium">{insight.recommendation}</p>
                                    <p className="text-xs text-gray-600 mt-1">{insight.reasoning}</p>
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader>
                              <CardTitle>Confidence Analysis</CardTitle>
                              <CardDescription>Analysis reliability metrics</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-4">
                                <div>
                                  <div className="flex justify-between text-sm mb-1">
                                    <span>Overall Confidence</span>
                                    <span>{result.confidenceMetrics.overallConfidence}%</span>
                                  </div>
                                  <Progress value={result.confidenceMetrics.overallConfidence} />
                                </div>

                                {Object.entries(result.confidenceMetrics.confidenceFactors).map(([factor, value]) => (
                                  <div key={factor}>
                                    <div className="flex justify-between text-sm mb-1">
                                      <span className="capitalize">{factor.replace('_', ' ')}</span>
                                      <span>{value}%</span>
                                    </div>
                                    <Progress value={value} className="h-2" />
                                  </div>
                                ))}

                                <div className="mt-4">
                                  <h4 className="font-medium text-sm mb-2">Confidence Reasons:</h4>
                                  <ul className="text-xs text-gray-600 space-y-1">
                                    {result.confidenceMetrics.confidenceReasons.map((reason, i) => (
                                      <li key={i} className="flex items-start space-x-2">
                                        <span className="text-green-500">✓</span>
                                        <span>{reason}</span>
                                      </li>
                                    ))}
                                  </ul>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </TabsContent>

                      <TabsContent value="demographics" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <Card>
                            <CardHeader>
                              <CardTitle>Demographic Insights</CardTitle>
                              <CardDescription>Target audience analysis</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-4">
                                <div>
                                  <h4 className="font-medium text-sm mb-2">Estimated Age Range:</h4>
                                  <p className="text-lg font-semibold text-purple-600">
                                    {result.demographicInsights.estimatedAge}
                                  </p>
                                </div>

                                <div>
                                  <h4 className="font-medium text-sm mb-2">Target Audience:</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {result.demographicInsights.targetAudience.map((audience, i) => (
                                      <Badge key={i} variant="outline" className="text-xs">
                                        {audience}
                                      </Badge>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>

                          <Card>
                            <CardHeader>
                              <CardTitle>Platform Optimization</CardTitle>
                              <CardDescription>Performance by dating platform</CardDescription>
                            </CardHeader>
                            <CardContent>
                              <div className="space-y-4">
                                {Object.entries(result.demographicInsights.platformOptimization).map(([platform, score]) => (
                                  <div key={platform}>
                                    <div className="flex justify-between text-sm mb-1">
                                      <span className="capitalize font-medium">{platform}</span>
                                      <span>{score}/100</span>
                                    </div>
                                    <Progress value={score} className="h-2" />
                                  </div>
                                ))}
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </TabsContent>

                      <TabsContent value="comparison" className="space-y-4">
                        <Card>
                          <CardHeader>
                            <CardTitle>Market Comparison</CardTitle>
                            <CardDescription>How you compare to other profiles</CardDescription>
                          </CardHeader>
                          <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              <div>
                                <h4 className="font-medium text-sm mb-4">Market Position</h4>
                                <div className="text-center p-6 bg-gray-50 rounded-lg">
                                  <div className="text-3xl font-bold text-purple-600 mb-2">
                                    {result.comparativeAnalysis.marketPosition.replace('_', ' ').toUpperCase()}
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    Top {result.comparativeAnalysis.topPercentile}% of profiles
                                  </p>
                                </div>
                              </div>

                              <div>
                                <h4 className="font-medium text-sm mb-4">Competitive Advantages</h4>
                                <div className="space-y-2">
                                  {result.comparativeAnalysis.competitiveAdvantages.map((advantage, i) => (
                                    <div key={i} className="flex items-center space-x-2">
                                      <span className="text-green-500">✓</span>
                                      <span className="text-sm">{advantage}</span>
                                    </div>
                                  ))}
                                </div>

                                <h4 className="font-medium text-sm mb-2 mt-4">Areas for Improvement</h4>
                                <div className="space-y-2">
                                  {result.comparativeAnalysis.areasForImprovement.map((area, i) => (
                                    <div key={i} className="flex items-center space-x-2">
                                      <span className="text-orange-500">→</span>
                                      <span className="text-sm">{area}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center mt-8">
              <Button asChild size="lg" className="mr-4">
                <Link to="/bio-analyzer-pro">
                  Analyze Bio with Advanced AI <Sparkles className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button variant="outline" onClick={() => {
                setStatus("idle");
                setResults([]);
                setImages([]);
              }}>
                Analyze More Photos
              </Button>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
