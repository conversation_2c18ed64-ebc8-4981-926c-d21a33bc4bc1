/* TinderOP Design System Utility Classes */

@layer components {
  /* === CONTAINER UTILITIES === */
  .container-section {
    @apply max-w-7xl mx-auto px-6 py-16;
  }
  
  .container-content {
    @apply max-w-4xl mx-auto px-6;
  }
  
  .container-narrow {
    @apply max-w-2xl mx-auto px-6;
  }
  
  /* === TYPOGRAPHY COMPONENTS === */
  .text-hero {
    @apply text-display-1 md:text-display-1-mobile font-bold text-foreground leading-tight;
  }
  
  .text-heading-1 {
    @apply text-h1 md:text-h1-mobile font-bold text-foreground;
  }
  
  .text-heading-2 {
    @apply text-h2 md:text-h2-mobile font-semibold text-foreground;
  }
  
  .text-body-large {
    @apply text-body-lg text-muted-foreground leading-relaxed;
  }
  
  .text-body {
    @apply text-body-md text-foreground leading-normal;
  }
  
  .text-caption {
    @apply text-caption text-muted-foreground;
  }
  
  /* === BUTTON COMPONENTS === */
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 bg-gradient-primary text-cloud-white font-semibold rounded-lg shadow-button-primary transition-all duration-200 hover:-translate-y-0.5 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-flame-red focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }
  
  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 border-2 border-flame-red text-flame-red bg-transparent font-semibold rounded-lg transition-all duration-200 hover:bg-flame-red hover:text-cloud-white focus:outline-none focus:ring-2 focus:ring-flame-red focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-tertiary {
    @apply inline-flex items-center justify-center px-4 py-2 text-flame-red font-semibold transition-all duration-200 hover:underline focus:outline-none focus:ring-2 focus:ring-flame-red focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-ghost {
    @apply inline-flex items-center justify-center px-4 py-2 text-foreground font-medium rounded-lg transition-all duration-200 hover:bg-muted hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  /* === FORM COMPONENTS === */
  .input-field {
    @apply w-full px-3 py-2 border border-input bg-background text-foreground rounded-md placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .input-label {
    @apply block text-sm font-medium text-foreground mb-2;
  }
  
  .input-error {
    @apply text-sm text-error-crimson mt-1;
  }
  
  .input-help {
    @apply text-sm text-muted-foreground mt-1;
  }
  
  /* === CARD COMPONENTS === */
  .card-base {
    @apply bg-card border border-border rounded-lg shadow-sm;
  }
  
  .card-content {
    @apply p-6;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-border;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-border bg-muted/50;
  }
  
  .card-elevated {
    @apply card-base shadow-md hover:shadow-lg transition-shadow duration-200;
  }
  
  /* === LAYOUT COMPONENTS === */
  .section-padding {
    @apply py-16 md:py-20 lg:py-24;
  }
  
  .section-padding-sm {
    @apply py-8 md:py-12;
  }
  
  .section-padding-lg {
    @apply py-20 md:py-28 lg:py-32;
  }
  
  .grid-auto-fit {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  .grid-auto-fill {
    @apply grid gap-6;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
  
  /* === SPACING UTILITIES === */
  .space-section {
    @apply space-y-16;
  }
  
  .space-content {
    @apply space-y-8;
  }
  
  .space-items {
    @apply space-y-4;
  }
  
  .space-tight {
    @apply space-y-2;
  }
  
  /* === STATUS COMPONENTS === */
  .status-success {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-success-green/10 text-success-green border border-success-green/20;
  }
  
  .status-warning {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-warning-amber/10 text-warning-amber border border-warning-amber/20;
  }
  
  .status-error {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-error-crimson/10 text-error-crimson border border-error-crimson/20;
  }
  
  .status-info {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-info-blue/10 text-info-blue border border-info-blue/20;
  }
  
  /* === FOCUS UTILITIES === */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }
  
  .focus-ring-primary {
    @apply focus:outline-none focus:ring-2 focus:ring-flame-red focus:ring-offset-2;
  }
  
  /* === ANIMATION UTILITIES === */
  .animate-fade-in {
    @apply animate-in fade-in duration-300;
  }
  
  .animate-slide-up {
    @apply animate-in slide-in-from-bottom-4 duration-300;
  }
  
  .animate-slide-down {
    @apply animate-in slide-in-from-top-4 duration-300;
  }
  
  .animate-scale-in {
    @apply animate-in zoom-in-95 duration-200;
  }
  
  /* === GRADIENT UTILITIES === */
  .gradient-text-primary {
    @apply bg-gradient-to-r from-flame-red to-sparks-pink bg-clip-text text-transparent;
  }
  
  .gradient-border {
    @apply relative;
  }
  
  .gradient-border::before {
    @apply absolute inset-0 rounded-lg p-px bg-gradient-primary;
    content: '';
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
  }
  
  /* === RESPONSIVE UTILITIES === */
  .responsive-grid-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }
  
  .responsive-grid-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }
  
  .responsive-grid-4 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6;
  }
  
  .responsive-flex {
    @apply flex flex-col md:flex-row gap-6;
  }
  
  /* === ACCESSIBILITY UTILITIES === */
  .sr-only {
    @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
    clip: rect(0, 0, 0, 0);
  }
  
  .skip-link {
    @apply sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-background focus:text-foreground focus:border focus:border-border focus:rounded-md;
  }
  
  /* === PRINT UTILITIES === */
  @media print {
    .print-hidden {
      @apply hidden;
    }
    
    .print-visible {
      @apply block;
    }
  }
}

/* === COMPONENT VARIANTS === */
@layer components {
  /* Button size variants */
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }
  
  .btn-lg {
    @apply px-8 py-4 text-lg;
  }
  
  .btn-icon {
    @apply p-2 w-10 h-10;
  }
  
  /* Input size variants */
  .input-sm {
    @apply px-2 py-1 text-sm;
  }
  
  .input-lg {
    @apply px-4 py-3 text-lg;
  }
  
  /* Card size variants */
  .card-sm {
    @apply p-4;
  }
  
  .card-lg {
    @apply p-8;
  }
}
