import { Routes, Route } from "react-router";
import { Layout } from "./components/Layout";
import { ProtectedRoute } from "./components/ProtectedRoute";
import { BioAnalyzer } from "./pages/BioAnalyzer";
import { ImageAnalyzer } from "./pages/ImageAnalyzer";
import BioAnalyzerPro from "./pages/BioAnalyzerPro";
import ImageAnalyzerPro from "./pages/ImageAnalyzerPro";
import AnalysisComparison from "./pages/AnalysisComparison";
import { LandingPage } from "./pages/LandingPage";
import { Dashboard } from "./pages/Dashboard";
import { AccountSettings } from "./pages/AccountSettings";
import { DesignSystemShowcase } from "./pages/DesignSystemShowcase";

export function AppRoutes() {
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route index element={<LandingPage />} />
        <Route
          path="dashboard"
          element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          }
        />
        <Route
          path="account-settings/*"
          element={
            <ProtectedRoute>
              <AccountSettings />
            </ProtectedRoute>
          }
        />
        <Route path="image-analyzer" element={<ImageAnalyzer />} />
        <Route path="bio-analyzer" element={<BioAnalyzer />} />
        <Route path="image-analyzer-pro" element={<ImageAnalyzerPro />} />
        <Route path="bio-analyzer-pro" element={<BioAnalyzerPro />} />
        <Route path="analysis-comparison" element={<AnalysisComparison />} />
        <Route path="design-system" element={<DesignSystemShowcase />} />
      </Route>
    </Routes>
  );
}
