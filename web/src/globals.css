@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import design system utilities */
@import './styles/design-system.css';

@layer base {
  :root {
    /* === BRAND COLORS === */
    --flame-red: 0 100% 66%;
    --sparks-pink: 0 100% 75%;
    --graphite-90: 0 0% 10%;
    --graphite-60: 0 0% 40%;
    --cloud-white: 0 0% 100%;

    /* === SEMANTIC COLORS === */
    --success-green: 134 61% 41%;
    --warning-amber: 45 100% 51%;
    --error-crimson: 354 70% 54%;
    --info-blue: 188 78% 41%;

    /* === SYSTEM COLORS (Light Theme) === */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 100% 66%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 100% 66%;

    /* === SPACING SCALE === */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */
    --space-4xl: 6rem;      /* 96px */

    /* === TYPOGRAPHY === */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* === BORDER RADIUS === */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 1rem;      /* 16px */
    --radius-xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;
    --radius: 0.5rem;

    /* === SHADOWS === */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-button-primary: 0 4px 8px rgba(255, 88, 81, 0.35);

    /* === CHART COLORS === */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* === SIDEBAR === */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    /* === SYSTEM COLORS (Dark Theme) === */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 100% 66%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 100% 66%;

    /* === CHART COLORS (Dark Theme) === */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* === SIDEBAR (Dark Theme) === */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* === SHADOWS (Dark Theme) === */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --shadow-button-primary: 0 4px 8px rgba(255, 88, 81, 0.5);
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* === SPACING UTILITIES === */
  .space-xs { @apply space-y-1; }
  .space-sm { @apply space-y-2; }
  .space-md { @apply space-y-4; }
  .space-lg { @apply space-y-6; }
  .space-xl { @apply space-y-8; }
  .space-2xl { @apply space-y-12; }
  .space-3xl { @apply space-y-16; }
  .space-4xl { @apply space-y-24; }

  /* === TYPOGRAPHY UTILITIES === */
  .text-display-1 {
    font-size: 3.5rem;
    line-height: 4rem;
    font-weight: 700;
    letter-spacing: -0.01em;
  }

  .text-display-1-mobile {
    font-size: 2.5rem;
    line-height: 3rem;
    font-weight: 700;
    letter-spacing: -0.01em;
  }

  /* === GRADIENT UTILITIES === */
  .bg-gradient-primary {
    background: linear-gradient(135deg, hsl(var(--flame-red)) 0%, hsl(var(--sparks-pink)) 100%);
  }

  .bg-gradient-hero {
    background: linear-gradient(180deg, #FFF2F1 0%, #FFFFFF 60%);
  }

  /* === SHADOW UTILITIES === */
  .shadow-button-primary {
    box-shadow: var(--shadow-button-primary);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: var(--font-family-primary);
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-primary);
    font-weight: 600;
  }

  code, pre {
    font-family: var(--font-family-mono);
  }
}
