# Clerk Authentication Revert Guide

This document provides step-by-step instructions to restore Clerk authentication functionality that was temporarily commented out for development purposes.

## Overview

The following Clerk authentication components were commented out:
- ClerkProvider wrapper in main.tsx
- ProtectedRoute authentication logic
- UserButton and UserProfile components
- useUser hooks and related functionality

## Files Modified

### 1. `/src/main.tsx`
**Current State (Commented Out):**
```typescript
// import { ClerkProvider } from "@clerk/react-router";
// const publishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;
// if (!publishableKey) {
//   throw new Error("Missing Publishable Key");
// }

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter>
      {/* <ClerkProvider publishableKey={publishableKey}> */}
        <PrivacyManager>
          <AppRoutes />
        </PrivacyManager>
      {/* </ClerkProvider> */}
    </BrowserRouter>
  </StrictMode>
);
```

**To Revert:**
```typescript
import { ClerkProvider } from "@clerk/react-router";

const publishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!publishableKey) {
  throw new Error("Missing Publishable Key");
}

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <BrowserRouter>
      <ClerkProvider publishableKey={publishableKey}>
        <PrivacyManager>
          <AppRoutes />
        </PrivacyManager>
      </ClerkProvider>
    </BrowserRouter>
  </StrictMode>
);
```

### 2. `/src/components/ProtectedRoute.tsx`
**Current State (Bypassed):**
```typescript
// import { SignedIn, SignedOut, RedirectToSignIn } from "@clerk/react-router";

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  // Temporarily bypass authentication - just render children directly
  return <>{children}</>;
  
  // Original Clerk authentication logic (commented out)
  // return (
  //   <>
  //     <SignedIn>
  //       {children}
  //     </SignedIn>
  //     <SignedOut>
  //       <RedirectToSignIn />
  //     </SignedOut>
  //   </>
  // );
}
```

**To Revert:**
```typescript
import { SignedIn, SignedOut, RedirectToSignIn } from "@clerk/react-router";

export function ProtectedRoute({ children }: ProtectedRouteProps) {
  return (
    <>
      <SignedIn>
        {children}
      </SignedIn>
      <SignedOut>
        <RedirectToSignIn />
      </SignedOut>
    </>
  );
}
```

### 3. `/src/pages/Dashboard.tsx`
**Current State (Mock Data):**
```typescript
// import { UserButton, useUser } from "@clerk/react-router";

export function Dashboard() {
  // const { user } = useUser();
  // Mock user data for now (replace with actual auth later)
  const user = {
    firstName: "Demo",
    lastName: "User",
    emailAddresses: [{ emailAddress: "<EMAIL>" }]
  };

  // In JSX:
  {/* <UserButton /> */}
  <Button variant="secondary" size="sm">
    <Settings className="h-4 w-4 mr-2" />
    Settings
  </Button>
}
```

**To Revert:**
```typescript
import { UserButton, useUser } from "@clerk/react-router";

export function Dashboard() {
  const { user } = useUser();

  // In JSX:
  <UserButton />
}
```

### 4. `/src/pages/AccountSettings.tsx`
**Current State (Mock Data):**
```typescript
// import { UserProfile, useUser } from "@clerk/react-router";

export function AccountSettings() {
  // const { user } = useUser();
  // Mock user data for now (replace with actual auth later)
  const user = {
    firstName: "Demo",
    lastName: "User",
    fullName: "Demo User",
    emailAddresses: [{ emailAddress: "<EMAIL>" }],
    primaryEmailAddress: { emailAddress: "<EMAIL>" },
    createdAt: new Date("2024-01-01").getTime()
  };

  // In JSX:
  <CardContent className="p-6">
    {/* <UserProfile ... /> */}
    <div className="space-y-4">
      <div className="text-center py-8">
        <p className="text-muted-foreground">
          User profile management temporarily disabled.
        </p>
      </div>
    </div>
  </CardContent>
}
```

**To Revert:**
```typescript
import { UserProfile, useUser } from "@clerk/react-router";

export function AccountSettings() {
  const { user } = useUser();

  // In JSX:
  <CardContent className="p-0 overflow-hidden">
    <div className="w-full max-w-full">
      <UserProfile
        appearance={{
          elements: {
            rootBox: "w-full max-w-full",
            card: "shadow-none border-none rounded-none w-full max-w-full",
            navbarMobileMenuButton: "hidden",
          },
        }}
      />
    </div>
  </CardContent>
}
```

## Environment Variables Required

When reverting, ensure you have the following environment variable set:

### `.env` file:
```bash
VITE_CLERK_PUBLISHABLE_KEY=pk_test_your_clerk_publishable_key_here
```

### `.env.example` file:
```bash
VITE_CLERK_PUBLISHABLE_KEY=
```

## Step-by-Step Revert Process

1. **Set up Clerk Environment Variables:**
   - Add your Clerk publishable key to `.env`
   - Update `.env.example` to include the Clerk variable

2. **Restore main.tsx:**
   - Uncomment ClerkProvider import
   - Uncomment publishableKey logic
   - Uncomment ClerkProvider wrapper

3. **Restore ProtectedRoute.tsx:**
   - Uncomment Clerk imports
   - Replace bypass logic with original authentication logic

4. **Restore Dashboard.tsx:**
   - Uncomment Clerk imports
   - Replace mock user data with useUser hook
   - Replace Settings button with UserButton

5. **Restore AccountSettings.tsx:**
   - Uncomment Clerk imports
   - Replace mock user data with useUser hook
   - Replace placeholder content with UserProfile component

6. **Test the Authentication:**
   - Run `bun run typecheck` to ensure no TypeScript errors
   - Run `bun run dev` to start the development server
   - Test sign-in/sign-out functionality
   - Verify protected routes redirect to sign-in when not authenticated

## Dependencies

The Clerk package is already installed in package.json:
```json
"@clerk/react-router": "^1.6.3"
```

No additional installation is required when reverting.

## Notes

- All Clerk functionality was commented out, not deleted, making reversion straightforward
- Mock user data was added to maintain TypeScript compatibility during development
- The ProtectedRoute component currently bypasses authentication entirely
- Button variant was changed from "outline" to "secondary" to match the custom button component variants

## Verification Checklist

After reverting:
- [ ] TypeScript compilation passes without errors
- [ ] Application starts without console errors
- [ ] Sign-in page appears for unauthenticated users
- [ ] Protected routes (Dashboard, Account Settings) require authentication
- [ ] UserButton appears and functions correctly
- [ ] UserProfile component renders in Account Settings
- [ ] Environment variables are properly configured
